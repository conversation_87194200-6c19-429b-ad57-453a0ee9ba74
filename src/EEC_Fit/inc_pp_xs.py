import sys
import os
import numpy as np
import lhapdf
import vegas
from typing import Tuple
from enum import Enum, auto

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.EEC_Fit.als_running import AlphaSRunner
from src.EEC_Fit.phase_space_pp import PhaseSpace


# 定义一个进程本地缓存，用于安全地在多进程环境中初始化和存储LHAPDF对象。
_PDF_CACHE = {}

# 防止Vegas多线程环境下出现问题
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"


class Channel(Enum):
    qiqj_qX = auto()
    qiqbarj_qX = auto()
    qiqbari_qjX = auto()
    qiqi_qX = auto()
    qiqbari_qiX = auto()
    qiqbari_gX = auto()
    qg_qX = auto()
    qg_gX = auto()
    gg_gX = auto()
    gg_qX = auto()


class pp_jet_cross_section_calculator:

    NC = 3.0
    CF = (NC**2 - 1.0) / (2.0 * NC)

    QUARK_FLAVORS = [1, 2, 3, 4]
    ANTI_QUARK_FLAVORS = [-1, -2, -3, -4]
    NF = float(len(QUARK_FLAVORS))

    def __init__(
        self,
        pdf: lhapdf.PDF,
        alphas_runner: AlphaSRunner,
        Ecom: float,
        radius: float,
        muR_factor: float = 1.0,
        muF_factor: float = 1.0,
    ):
        """
        初始化pp_jet_cross_section_calculator

        Args:
            pdf_name: PDF集合名称
            alphas_runner: 强耦合常数运行器
            Ecom: 对撞机质心系能量
            radius: Jet半径
            muR_factor: 重整化标度因子 κ_R
            muF_factor: 因子化标度因子 κ_F
        """
        self.pdf = pdf
        self.alphas_runner = alphas_runner
        self.Ecom = Ecom
        self.radius = radius
        self.muR_factor = muR_factor
        self.muF_factor = muF_factor

        # 初始化相空间计算器
        self.phase_space = PhaseSpace(Ecom=Ecom)
        self.z_boson_mass = self.phase_space.MZ
        self.z_boson_width = self.phase_space.GZ

        self.ALL_CHANNELS = list(Channel)
        self.num_channels = len(self.ALL_CHANNELS)

    def _calculate_luminosity(self, channel: Channel, pdf, x1, x2, muF):
        """Translates the C++ pdf_prd function."""
        lumi = 0.0

        # Use a small epsilon for float comparison
        if x1 <= 0 or x2 <= 0:
            return 0.0

        # Helper lambda to match C++ 'pdf_pair'
        pdf_pair = lambda i, j: pdf.xfxQ(i, x1, muF) * pdf.xfxQ(j, x2, muF)

        # All quark and anti-quark flavors
        all_quarks = self.QUARK_FLAVORS + self.ANTI_QUARK_FLAVORS
        g = 21

        if channel == Channel.qiqj_qX:  # q_i q_j
            for i in all_quarks:
                for j in all_quarks:
                    if abs(i) != abs(j):
                        lumi += pdf_pair(i, j)

        elif channel == Channel.qiqbarj_qX:  # q_i qbar_j
            for i in self.QUARK_FLAVORS:
                for j in self.QUARK_FLAVORS:
                    if i != j:
                        lumi += pdf_pair(i, -j) + pdf_pair(-i, j)

        elif channel == Channel.qiqbari_qjX:  # q_i qbar_i
            for i in self.QUARK_FLAVORS:
                lumi += pdf_pair(i, -i) + pdf_pair(-i, i)

        elif channel == Channel.qiqi_qX:  # q_i q_i
            for i in all_quarks:
                lumi += pdf_pair(i, i)

        elif channel == Channel.qiqbari_qiX:  # q_i qbar_i
            for i in self.QUARK_FLAVORS:
                lumi += pdf_pair(i, -i) + pdf_pair(-i, i)

        elif channel == Channel.qiqbari_gX:  # q_i qbar_i
            for i in self.QUARK_FLAVORS:
                lumi += pdf_pair(i, -i) + pdf_pair(-i, i)

        elif channel == Channel.qg_qX or channel == Channel.qg_gX:  # q g
            for i in all_quarks:
                lumi += pdf_pair(i, g) + pdf_pair(g, i)

        elif channel == Channel.gg_gX or channel == Channel.gg_qX:  # g g
            lumi += pdf_pair(g, g)

        return lumi

    def _calculate_matrix_element_sq(self, channel: Channel, v: float):
        """
        Translates the matrix element part of the C++ dsigma_dvdw function.
        Strictly follows the C++ implementation without simplification.
        """
        # Guard against division by zero or invalid v
        epsilon = 1e-9
        if v <= epsilon or v >= 1.0 - epsilon:
            return 0.0

        v2 = v * v
        one_minus_v = 1.0 - v
        one_minus_v_sq = one_minus_v * one_minus_v

        NC, CF, NF = self.NC, self.CF, self.NF

        matrix_element_sq = 0.0

        if channel == Channel.qiqj_qX or channel == Channel.qiqbarj_qX:
            # C++: CF / NC * (1 + pow(v, 2)) / pow(1 - v, 2)
            term_v = CF / NC * (1 + v2) / one_minus_v_sq
            # C++: CF / NC * (1 + pow(1 - v, 2)) / pow(v, 2)
            term_one_minus_v = CF / NC * (1 + one_minus_v_sq) / v2
            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqbari_qjX:
            # C++: CF / NC * (1.0 + 2 * pow(v, 2) - 2 * v) * (NF - 1.0)
            term_v = CF / NC * (1.0 + 2 * v2 - 2 * v) * (NF - 1.0)
            # C++: CF / NC * (1.0 + 2 * pow(1 - v, 2) - 2 * (1 - v)) * (NF - 1.0)
            term_one_minus_v = (
                CF / NC * (1.0 + 2 * one_minus_v_sq - 2 * one_minus_v) * (NF - 1.0)
            )
            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqi_qX:
            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(v, 4) - ... ) / pow(1 - v, 2) / pow(v, 2) / 2.0
            num_v = (
                NC * v**4
                - 2.0 * NC * v**3
                + 4.0 * NC * v2
                + v2
                - (3.0 * NC + 1) * v
                + NC
            )
            term_v = (2.0 * CF / (NC**2)) * num_v / one_minus_v_sq / v2 / 2.0

            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(1 - v, 4) - ... ) / pow(v, 2) / pow(1 - v, 2) / 2.0
            num_one_minus_v = (
                NC * one_minus_v**4
                - 2.0 * NC * one_minus_v**3
                + 4.0 * NC * one_minus_v_sq
                + one_minus_v_sq
                - (3.0 * NC + 1) * one_minus_v
                + NC
            )
            term_one_minus_v = (
                (2.0 * CF / (NC**2)) * num_one_minus_v / v2 / one_minus_v_sq / 2.0
            )

            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqbari_qiX:
            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(v, 4) - (3.0 * NC + 1.0) * pow(v, 3) + ...) / pow(1 - v, 2)
            num_v = (
                NC * v**4
                - (3.0 * NC + 1.0) * v**3
                + (4 * NC + 1.0) * v2
                - (2.0 * NC) * v
                + NC
            )
            term_v = (2.0 * CF / (NC**2)) * num_v / one_minus_v_sq

            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(1 - v, 4) - (3.0 * NC + 1.0) * pow(1 - v, 3) + ...) / pow(v, 2)
            num_one_minus_v = (
                NC * one_minus_v**4
                - (3.0 * NC + 1.0) * one_minus_v**3
                + (4 * NC + 1.0) * one_minus_v_sq
                - (2.0 * NC) * one_minus_v
                + NC
            )
            term_one_minus_v = (2.0 * CF / (NC**2)) * num_one_minus_v / v2

            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqbari_gX:
            # C++ term_v part 1: CF / pow(NC, 2) * (2.0 * pow(v, 2) - 2.0 * v + 1.0)
            p1_v = CF / (NC**2) * (2.0 * v2 - 2.0 * v + 1.0)
            # C++ term_v part 2: (2.0 * pow(NC, 2) * pow(v, 2) - ... - 1.0)
            p2_v = 2.0 * (NC**2) * v2 - 2.0 * (NC**2) * v + (NC**2) - 1.0
            # C++ term_v full: ... / v / (1 - v) / 2.0
            term_v = p1_v * p2_v / v / one_minus_v / 2.0

            # C++ term_one_minus_v (similar structure)
            p1_omv = CF / (NC**2) * (2.0 * one_minus_v_sq - 2.0 * one_minus_v + 1.0)
            p2_omv = (
                2.0 * (NC**2) * one_minus_v_sq
                - 2.0 * (NC**2) * one_minus_v
                + (NC**2)
                - 1.0
            )
            term_one_minus_v = p1_omv * p2_omv / one_minus_v / v / 2.0

            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qg_qX:
            # C++: (pow(v, 2) + 1.0) * ((pow(NC, 2) - 1.0) * pow(v, 2) + 2.0 * v + pow(NC, 2) - 1.0) / 2.0 / pow(NC, 2) / v / pow(1 - v, 2)
            num = (v2 + 1.0) * ((NC**2 - 1.0) * v2 + 2.0 * v + NC**2 - 1.0)
            matrix_element_sq = num / (2.0 * (NC**2) * v * one_minus_v_sq)

        elif channel == Channel.qg_gX:
            # C++: (pow(1 - v, 2) + 1.0) * ((pow(NC, 2) - 1.0) * pow(1 - v, 2) + 2.0 * (1 - v) + pow(NC, 2) - 1.0) / 2.0 / pow(NC, 2) / (1 - v) / pow(v, 2)
            num = (one_minus_v_sq + 1.0) * (
                (NC**2 - 1.0) * one_minus_v_sq + 2.0 * one_minus_v + NC**2 - 1.0
            )
            matrix_element_sq = num / (2.0 * (NC**2) * one_minus_v * v2)

        elif channel == Channel.gg_gX:
            # C++ term_v: (pow(pow(v, 2) - v + 1.0, 3) / pow(v, 2) / pow(1 - v, 2)) * 9.0 / 2.0 / 2.0
            term_v = (pow(v2 - v + 1.0, 3) / v2 / one_minus_v_sq) * 9.0 / 2.0 / 2.0

            # C++ term_one_minus_v: (pow(pow(1 - v, 2) - (1 - v) + 1.0, 3) / pow(1 - v, 2) / pow(v, 2)) * 9.0 / 2.0 / 2.0
            term_one_minus_v = (
                (pow(one_minus_v_sq - one_minus_v + 1.0, 3) / one_minus_v_sq / v2)
                * 9.0
                / 2.0
                / 2.0
            )

            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.gg_qX:
            # C++ common factor: 1 / (2.0 * NC * (pow(NC, 2) - 1.0))
            common_pre = 1.0 / (2.0 * NC * (NC**2 - 1.0))

            # C++ term_v: ... * (pow(v, 2) + pow(1 - v, 2)) * (2.0 * pow(NC, 2) * (pow(v, 2) - v) + pow(NC, 2) - 1) / (1 - v) / v * (NF)
            num_v = (v2 + one_minus_v_sq) * (2.0 * NC**2 * (v2 - v) + NC**2 - 1.0)
            term_v = common_pre * num_v / one_minus_v / v * NF

            # C++ term_one_minus_v: ... * (pow(1 - v, 2) + pow(v, 2)) * (2.0 * pow(NC, 2) * (pow(1 - v, 2) - (1 - v)) + pow(NC, 2) - 1) / (1 - v) / v * (NF)
            num_omv = (one_minus_v_sq + v2) * (
                2.0 * NC**2 * (one_minus_v_sq - one_minus_v) + NC**2 - 1.0
            )
            term_one_minus_v = common_pre * num_omv / one_minus_v / v * NF

            matrix_element_sq = term_v + term_one_minus_v

        return matrix_element_sq

    def _single_event_weight(
        self, x1: float, x2: float, zc: float, pt: float, eta: float, pdf_name: str
    ) -> np.ndarray:
        """
        计算单个事件的权重。

        Parameters
        ----------
        v : float
            无量纲变量V
        w : float
            无量纲变量W
        zc : float
            截断参数Zc
        pt : float
            横动量，单位为GeV
        eta : float
            赝快度

        Returns
        -------
        float
        """
        channel_results = np.zeros(self.num_channels, dtype=float)
        zero_vector = channel_results.copy()

        shat = self.phase_space.shat(x1, x2)
        V = self.phase_space.v_var(pt, eta)
        W = self.phase_space.w_var(pt, eta)
        muF = self.muF_factor * pt
        muR = self.muR_factor * pt

        alphas_muF = self.alphas_runner.alpha_s(muF)
        alphas_muR = self.alphas_runner.alpha_s(muR)

        ##########Part1: LO_zc=1##########
        LO_delta_zc = 1.0
        LO_delta_pThat = self.phase_space.pthat(LO_delta_zc, pt)
        LO_delta_etahat = self.phase_space.etahat(x1, x2, eta)

        v = 1 - LO_delta_pThat / x2 / np.sqrt(self.Ecom**2) * np.exp(-eta)

        for i, channel in enumerate(self.ALL_CHANNELS):
            lumi = self._calculate_luminosity(channel, self.pdf, x1, x2, muF)
            if lumi <= 0:
                continue

            me_sq = self._calculate_matrix_element_sq(channel, v)
            if me_sq <= 0:
                continue

            total_partonic_xs += lumi * me_sq
            channel_results[i] = lumi * me_sq

        total_partonic_xs *= np.pi * alphas_muR**2 / (2.0 * shat)
        channel_results *= np.pi * alphas_muR**2 / (2.0 * shat)
        ##########Part2: NLO_zc##########
        return channel_results


class Test_VegasIntegrand:

    def __init__(
        self,
        pdf_name: str,
        alphas_runner: AlphaSRunner,
        Ecom: float,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        R: float,
        kappa_R: float = 1.0,
        kappa_F: float = 1.0,
    ):
        self.pdf_name = pdf_name
        self.alphas_runner = alphas_runner
        self.Ecom = Ecom
        self.kappa_R = kappa_R
        self.kappa_F = kappa_F
        self.pT_range = pT_range
        self.eta_range = eta_range
        self.R = R

        self.phase_space = PhaseSpace(Ecom=Ecom)
        self.weight = pp_jet_cross_section_calculator(
            pdf_name, alphas_runner, Ecom, R, kappa_R, kappa_F
        )

    def __call__(self, y: np.ndarray) -> np.ndarray:
        pid = os.getpid()
        if pid not in _PDF_CACHE:
            lhapdf.setVerbosity(0)
            _PDF_CACHE[pid] = lhapdf.mkPDF(self.pdf_name)
        pdf = _PDF_CACHE[pid]

        y = np.atleast_2d(y)
        N = y.shape[0]
        # Output is now a single value (total cross section), so shape is (N,).
        out = np.empty((N, 16), dtype=float)

        for i in range(N):
            out[i] = self._single_event(y[i, 0], y[i, 1], y[i, 2], y[i, 3], pdf)
        return out

    def _single_event(self, y_pt, y_eta, y_x1, y_x2, pdf):
        pT = self.pT_range[0] + y_pt * (self.pT_range[1] - self.pT_range[0])
        eta = self.eta_range[0] + y_eta * (self.eta_range[1] - self.eta_range[0])
        jacobian = (self.pT_range[1] - self.pT_range[0]) * (
            self.eta_range[1] - self.eta_range[0]
        )

        V = self.phase_space.v_var(pT, eta)
        W = self.phase_space.w_var(pT, eta)

        x1min = self.phase_space.x1min(pT, eta)
        x2min = self.phase_space.x2min(x1min, pT, eta)

        x1 = x1min + y_x1 * (1 - x1min)
        if x1 <= 0 or x1 >= 1 or x1min < 0 or x1min >= 1:
            return 0.0

        x2 = x2min + y_x2 * (1 - x2min)
        if x2 <= 0 or x2 >= 1 or x2min <= 0 or x2min >= 1:
            return 0.0

        zcmin = self.phase_space.zcmin(x1, x2, pT, eta)
        if zcmin >= 1 or zcmin < 0:
            return 0.0

        ##########LO_zc=1##########
        zc = 1.0
        pThat = pT / zc

        x2_z1 = (
            pThat * np.exp(-eta) / (np.sqrt(self.Ecom**2) - pThat * np.exp(eta) / x1)
        )

        etahat = eta - np.log(x1 / x2_z1) / 2
        shat = x1 * x2_z1 * self.Ecom**2
        vhat = 1 - pThat / np.sqrt(shat) * np.exp(-etahat)

        jacobian = (
            jacobian
            * (1 - x1min)
            / zc**2
            / (
                1
                / x2_z1**2
                * pThat**2
                * x1
                / (
                    x1
                    * np.sqrt(self.Ecom**2)
                    * (1 - pThat * np.exp(-eta) / (x2_z1 * np.sqrt(self.Ecom**2)))
                )
                ** 2
            )
            * 2
            * pT
            / self.Ecom**2
            / vhat
            * 0.3893792922e9
        )

        return self.weight._single_event_weight(x1, x2, zc, pT, eta, pdf)

    def calculate(self, num_samples: int = 1000000) -> np.ndarray:
        integrand = self
        integration_bounds = [
            [0, 1],  # y_pt
            [0, 1],  # y_eta
            [0, 1],  # y_x1
            [0, 1],  # y_x2
        ]

        integ = vegas.Integrator(integration_bounds)
        result = integ(integrand, nitn=10, neval=num_samples, mineval=num_samples // 10)
        return result


if __name__ == "__main__":
    test = Test_VegasIntegrand(
        "NNPDF31_nlo_as_0118",
        AlphaSRunner(0.118),
        14000,
        (100, 1000),
        (-2, 2),
        0.4,
        kappa_R=1.0,
        kappa_F=1.0,
    )

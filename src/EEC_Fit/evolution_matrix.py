# 导入必要的库和模块
import numpy as np
from scipy.linalg import solve_sylvester, expm
from typing import Tuple, Dict, List, Optional

from EEC_Fit.als_running import AlphaSRunner


class EvolutionMatrix:
    """
    用于计算QCD演化矩阵的类。

    该类实现了mellin空间下N=5的演化矩阵计算，支持从领头对数阶到次次领头对数阶的微扰QCD计算。
    使用重整化群方程(RGE)来演化部分子分布函数(PDF)或其他物理量。

    Parameters
    ----------
    order : int, optional
        微扰展开的阶数，默认为2（对应NNLL）
        - 0: LL (领头对数)
        - 1: NLL (次领头对数)
        - 2: NNLL (次次领头对数)
    kappa : float, optional
        能标缩放因子，默认为1.0
    ALZ_runner : AlphaSRunner, optional
        AlphaSRunner实例，用于计算强耦合常数。如果为None，则使用默认参数创建

    Attributes
    ----------
    order : int
        微扰对数的阶数
    kappa : float
        能标缩放因子
    ALZ_runner : AlphaSRunner
        强耦合常数计算器

    Raises
    ------
    ValueError
        当order为负数时抛出异常

    Notes
    -----
    - 使用缓存机制优化重复计算
    - 自动处理夸克质量阈值处的匹配
    - 支持向上和向下演化

    Examples
    --------
    >>> evolver = EvolutionMatrix(order=2, kappa=1.0)
    >>> pdf0 = np.array([1.0, 0.5])
    >>> pdf_evolved = evolver.evolve(pdf0, Q0=100.0, Q=200.0, R=0.4)
    """

    def __init__(
        self,
        order: int = 2,
        kappa: float = 1.0,
        ALZ_runner: Optional[AlphaSRunner] = None,
    ):
        """
        初始化EvolutionMatrix实例。

        Parameters
        ----------
        order : int, optional
            微扰展开的阶数，默认为2
        kappa : float, optional
            能标缩放因子，默认为1.0
        ALZ_runner : AlphaSRunner, optional
            AlphaSRunner实例，默认为None

        Raises
        ------
        ValueError
            当order为负数时抛出异常
        """
        if order < 0:
            raise ValueError("阶数必须是非负整数")

        self._cache: Dict[Tuple[int, int], Tuple[Dict, Dict]] = {}
        self.order = order
        self.kappa = kappa

        # 初始化或使用提供的ALZ_runner
        if ALZ_runner is None:
            self.ALZ_runner = AlphaSRunner(ALS_MZ=0.118)
        else:
            self.ALZ_runner = ALZ_runner

        # 从ALZ_runner获取物理常数
        self.CF = self.ALZ_runner.CF
        self.CA = self.ALZ_runner.CA
        self.ZETA2 = self.ALZ_runner.ZETA2
        self.ZETA3 = self.ALZ_runner.ZETA3
        self.ZETA4 = self.ALZ_runner.ZETA4
        self.ZETA5 = self.ALZ_runner.ZETA5
        self.PI = self.ALZ_runner.PI

    def evolve(
        self, pdf0: np.ndarray, Q0: float, Q: float, R: float, m: int = 4
    ) -> np.ndarray:
        """
        演化部分子分布函数(PDF)从能标Q0到Q。

        Parameters
        ----------
        pdf0 : np.ndarray
            初始能标Q0处的PDF向量，形状为(2,)，包含[夸克分量, 胶子分量]
        Q0 : float
            初始能标，单位为GeV
        Q : float
            目标能标，单位为GeV
        R : float
            角度参数（用于EEC计算）
        m : int, optional
            截断解的最大阶数，默认为2

        Returns
        -------
        np.ndarray
            演化后的PDF向量，形状为(2,)

        Notes
        -----
        - 当Q0 == Q时，直接返回原始PDF
        - 自动处理夸克质量阈值处的匹配
        - 支持向上演化(Q > Q0)和向下演化(Q < Q0)
        """
        if Q0 == Q:
            return pdf0

        evolution_operator = self.get_evolution_operator(Q0, Q, R, m)
        return evolution_operator @ pdf0

    def get_evolution_operator(
        self, Q0: float, Q: float, R: float = 1.0, m: int = 2
    ) -> np.ndarray:
        """
        计算从能标Q0到Q的演化算符矩阵。

        Parameters
        ----------
        Q0 : float
            初始能标，单位为GeV
        Q : float
            目标能标，单位为GeV
        R : float, optional
            角度参数，默认为1.0
        m : int, optional
            截断解的最大阶数，默认为2

        Returns
        -------
        np.ndarray
            2x2演化算符矩阵

        Notes
        -----
        - 当Q0 == Q时，返回单位矩阵
        - 自动分段处理跨越夸克质量阈值的演化
        - 使用矩阵指数和Sylvester方程求解
        """
        if Q0 == Q:
            return np.eye(2)

        # 获取演化路径和对应的活跃夸克数
        path, nfs = self._get_scale_nodes(Q0, Q, self.kappa, R)
        total_operator = np.eye(2)

        # 逐段计算演化算符并累乘
        for i in range(len(path) - 1):
            q1, q2 = path[i], path[i + 1]
            nf = nfs[i]
            segment_operator = self._get_single_segment_operator(q1, q2, nf, m)
            total_operator = segment_operator @ total_operator

        return total_operator

    def get_operator_fit(self, Q: float, R: float = 1.0) -> np.ndarray:
        """
        计算拟合用的简化演化算符（仅使用领头阶）。

        该方法使用领头阶(LO)近似计算演化算符，适用于快速拟合场景。

        Parameters
        ----------
        Q : float
            能标，单位为GeV
        R : float, optional
            角度参数，默认为1.0

        Returns
        -------
        np.ndarray
            2x2简化演化算符矩阵

        Notes
        -----
        - 仅使用gamma_0和beta_0（领头阶）
        - 计算速度快，适合大量重复计算的拟合场景
        - 精度低于完整的演化算符
        """
        nfs = self.ALZ_runner._nf_at(Q * self.kappa)
        _gamma_0 = self.gamma0(nfs)
        _beta_0 = self.ALZ_runner._beta0(nfs) * 4 * self.PI
        alpha_s_val = self.ALZ_runner.alpha_s(Q * R, self.kappa)
        return expm(np.log(alpha_s_val) * _gamma_0 / _beta_0)

    # ========== 内部核心计算方法 ==========

    def _get_single_segment_operator(
        self, Q0: float, Q: float, nf: int, m: int
    ) -> np.ndarray:
        """
        计算单个能标区间的演化算符。

        该方法计算在固定活跃夸克数nf下，从Q0到Q的演化算符。
        使用缓存机制避免重复计算R和U矩阵。

        Parameters
        ----------
        Q0 : float
            初始能标，单位为GeV
        Q : float
            目标能标，单位为GeV
        nf : int
            活跃夸克数
        m : int
            重求和的最大阶数

        Returns
        -------
        np.ndarray
            2x2演化算符矩阵

        Raises
        ------
        ValueError
            当alpha_s值非物理（<=0）时抛出异常

        Notes
        -----
        演化算符的形式为: U(Q, Q0) = S(Q) @ L @ S^(-1)(Q0)
        """
        cache_key = (nf, m)
        if cache_key not in self._cache:
            self._cache[cache_key] = self._compute_and_cache_matrices(nf, m)
        r_dict, u_dict = self._cache[cache_key]

        # 计算归一化的耦合常数 a = alpha_s / (4π)
        a0 = self.ALZ_runner.alpha_s(Q0) / (4 * self.PI)
        a = self.ALZ_runner.alpha_s(Q) / (4 * self.PI)
        a0 = 0.118 / (4 * self.PI)
        a = 0.1 / (4 * self.PI)
        # 检查物理性
        if a <= 0 or a0 <= 0 or a / a0 <= 0:
            raise ValueError(
                f"非物理的alpha_s值导致演化失败: "
                f"a({Q:.2f})={a:.4e}, a0({Q0:.2f})={a0:.4e}"
            )

        # 计算演化算符: U = S(Q) @ L @ S^(-1)(Q0)
        L = expm(-np.log(a / a0) * r_dict[0])  # LL
        S0 = self._calculate_s_matrix(Q0, m, u_dict)
        S = self._calculate_s_matrix(Q, m, u_dict)
        return S @ L @ np.linalg.inv(S0)

    def _calculate_s_matrix(
        self, Q: float, m: int, u_dict: Dict[int, np.ndarray]
    ) -> np.ndarray:
        """
        计算高阶修正项U矩阵的和。
        S = I + a*U_1 + a²*U_2 + ... + a^m*U_m

        Parameters
        ----------
        Q : float
            能标，单位为GeV
        m : int
            截断展开的最大阶数
        u_dict : Dict[int, np.ndarray]
            U矩阵的字典，键为阶数

        Returns
        -------
        np.ndarray
            2x2 S矩阵

        Notes
        -----
        - a = alpha_s / (4π) 是归一化的耦合常数
        - 使用微扰展开计算S矩阵
        """
        a = self.ALZ_runner.alpha_s(Q) / (4 * self.PI)
        S = np.eye(2)
        for k in range(1, m + 1):
            if k in u_dict:
                S += (a**k) * u_dict[k]
        return S

    def _compute_and_cache_matrices(self, nf: int, m: int) -> Tuple[dict, dict]:
        """
        计算并缓存R和U矩阵。

        Parameters
        ----------
        nf : int
            活跃夸克数
        m : int
            U矩阵截断的最大阶数

        Returns
        -------
        Tuple[dict, dict]
            (R矩阵字典, U矩阵字典)

        Notes
        -----
        - R矩阵是计算U矩阵的中间结果
        - U是指定阶数的高阶修正项
        - 使用缓存避免重复计算
        """
        r_dict = self._calculate_r_matrices(nf, n_max=self.order)
        u_dict = self._solve_u_matrices(r_dict, n_max=m)
        return r_dict, u_dict

    def _calculate_r_matrices(self, nf: int, n_max: int) -> Dict[int, np.ndarray]:
        """
        计算对角化后的反常维度矩阵R。

        R矩阵通过递推关系从gamma和beta函数计算得到:
        β₀ * R_n = -γ_n - Σ(β_k * R_{n-k})

        Parameters
        ----------
        nf : int
            活跃夸克数
        n_max : int
            计算的最大阶数，R矩阵大于阶数的值为零。

        Returns
        -------
        Dict[int, np.ndarray]
            R矩阵字典，键为阶数(0到n_max)，值为2x2矩阵

        Raises
        ------
        ValueError
            当beta_0为零时抛出异常

        Notes
        -----
        - R_0对应领头阶反常维度
        - 高阶R矩阵包含重求和效应
        - 根据self.order截断微扰级数
        """
        # 计算beta函数系数（已归一化到4π的幂次）
        b_known = [
            self.ALZ_runner._beta0(nf) * (4 * self.PI),
            self.ALZ_runner._beta1(nf) * (16 * self.PI**2),
            self.ALZ_runner._beta2(nf) * (64 * self.PI**3),
        ]

        # 反常维度矩阵
        g_known = [self.gamma0(nf), self.gamma1(nf), self.gamma2(nf)]
        # 大于order的项 R矩阵置零
        series_truncation_order = self.order
        # 默认Rn每一个元素为零，一般来说截断m要比self.order大
        r = {n: np.zeros((2, 2)) for n in range(10)}
        # 递推计算R矩阵
        for n in range(n_max + 1):
            # self.order
            g_n = (
                g_known[n]
                if n <= series_truncation_order and n < len(g_known)
                else np.zeros((2, 2))
            )

            # 计算求和项: Σ(β_k * R_{n-k})
            sum_term = np.zeros((2, 2))
            for k in range(1, n + 1):
                if k <= series_truncation_order and k < len(b_known):
                    b_k = b_known[k]
                    sum_term += b_k * r[n - k]

            # 检查beta_0是否为零
            if b_known[0] == 0:
                raise ValueError("beta_0为零，无法进行除法运算")

            # 计算R_n = (-γ_n - Σ) / β₀
            result = (-g_n - sum_term) / b_known[0]
            # 确保结果是2x2矩阵
            r[n] = np.asarray(result, dtype=np.float64).reshape(2, 2)

        return r

    def _solve_u_matrices(
        self, r_dict: Dict[int, np.ndarray], n_max: int
    ) -> Dict[int, np.ndarray]:
        """
        求解高阶修正项U矩阵。

        U矩阵通过求解Sylvester方程得到:
        A @ U_n + U_n @ B = C

        Parameters
        ----------
        r_dict : Dict[int, np.ndarray]
            R矩阵字典
        n_max : int
            计算的最大阶数

        Returns
        -------
        Dict[int, np.ndarray]
            U矩阵字典，键为阶数(1到n_max)，值为2x2矩阵

        Notes
        -----
        - U_0 = I（单位矩阵，不存储）
        - 使用scipy的solve_sylvester求解线性矩阵方程
        - U矩阵用于构造变换矩阵S
        """
        u_dict = {}
        R0 = r_dict[0]
        I = np.eye(2)

        # 逐阶求解U矩阵
        for n in range(1, n_max + 1):
            Rn = r_dict.get(n, np.zeros((2, 2)))

            # Sylvester方程的系数矩阵
            A = -R0 - n * I
            B = R0
            C = Rn + sum(r_dict[n - k] @ u_dict[k] for k in range(1, n))

            # 求解 A @ U_n + U_n @ B = C
            u_dict[n] = solve_sylvester(A, B, C)

        return u_dict

    def _get_scale_nodes(
        self, Q0: float, Q: float, kappa: float, R: float
    ) -> Tuple[List[float], List[int]]:
        """
        获取演化路径的能标节点和对应的活跃夸克数。

        该方法确定从Q0到Q的演化路径，并在夸克质量阈值处插入节点，
        以确保在每个区间内活跃夸克数保持不变。

        Parameters
        ----------
        Q0 : float
            初始能标，单位为GeV
        Q : float
            目标能标，单位为GeV
        kappa : float
            能标缩放因子
        R : float
            角度参数

        Returns
        -------
        Tuple[List[float], List[int]]
            (能标节点列表, 活跃夸克数列表)
            - 能标节点列表: 包含起点、终点和中间阈值的有序列表
            - 活跃夸克数列表: 每个区间对应的nf值

        Notes
        -----
        - 自动检测并插入夸克质量阈值
        - 支持向上演化(Q > Q0)和向下演化(Q < Q0)
        - 在每个区间中点计算nf以确保正确性
        """
        q_start, q_end = kappa * Q0 * R, kappa * Q * R

        # 收集所有节点：起点、终点和中间的阈值
        nodes = {q_start, q_end}
        for m_thresh, _ in self.ALZ_runner.THRESH:
            if min(q_start, q_end) <= m_thresh <= max(q_start, q_end):
                nodes.add(m_thresh)

        # 根据演化方向排序节点
        path = sorted(list(nodes), reverse=(q_end < q_start))

        # 计算每个区间的活跃夸克数（在区间中点处）
        nfs = [
            self.ALZ_runner._nf_at((path[i] + path[i + 1]) / 2.0)
            for i in range(len(path) - 1)
        ]

        return path, nfs

    # ========== 反常维度矩阵 ==========

    @staticmethod
    def gamma0(nf: int) -> np.ndarray:
        """
        计算领头阶(LO)反常维度矩阵γ₀。

        Parameters
        ----------
        nf : int
            活跃夸克数

        Returns
        -------
        np.ndarray
            2x2反常维度矩阵，形式为 [[γ_qq, γ_qg], [γ_gq, γ_gg]]

        Notes
        -----
        - γ_qq: 夸克→夸克的反常维度
        - γ_qg: 胶子→夸克的反常维度
        - γ_gq: 夸克→胶子的反常维度
        - γ_gg: 胶子→胶子的反常维度
        """
        CF, CA = AlphaSRunner.CF, AlphaSRunner.CA
        return np.array(
            [
                [91 * CF / 15, -32 * nf / 105],
                [-8 * CF / 15, 181 * CA / 35 + 2 * nf / 3],
            ]
        )

    @staticmethod
    def gamma1(nf: int) -> np.ndarray:
        """
        计算次领头阶(NLO)反常维度矩阵γ₁。

        Parameters
        ----------
        nf : int
            活跃夸克数

        Returns
        -------
        np.ndarray
            2x2反常维度矩阵

        Notes
        -----
        - 包含ζ(2)和ζ(3)的贡献
        - 精度高于领头阶
        """
        CF, CA = AlphaSRunner.CF, AlphaSRunner.CA
        Z2, Z3 = AlphaSRunner.ZETA2, AlphaSRunner.ZETA3

        # 夸克→夸克分量
        qq = (
            -604601 * CF * nf / 110250
            + CF**2 * (-474221 / 13500 + 24 * Z2 - 16 * Z3)
            + CA * CF * (520837 / 6750 - 544 * Z2 / 15 + 8 * Z3)
        )

        # 胶子→夸克分量
        qg = (
            (-19792 * CF * nf) / 7875
            - (1024 * nf**2) / 3675
            + CA * nf * (1999 / 18375 + (128 * Z2) / 105)
        )

        # 夸克→胶子分量
        gq = CA * CF * (-2882863 / 661500 - (32 * Z2) / 15) + CF**2 * (
            -9374 / 3375 + (64 * Z2) / 15
        )

        # 胶子→胶子分量
        gg = (
            (340066 * CF * nf) / 165375
            + CA * nf * (26399 / 33075 - (16 * Z2) / 3)
            + CA**2 * (4706626 / 165375 - (632 * Z2) / 105 - 8 * Z3)
        )

        return np.array([[qq, qg], [gq, gg]])

    @staticmethod
    def gamma2(nf: int) -> np.ndarray:
        """
        计算次次领头阶(NNLO)反常维度矩阵γ₂。

        Parameters
        ----------
        nf : int
            活跃夸克数

        Returns
        -------
        np.ndarray
            2x2反常维度矩阵

        Notes
        -----
        - 包含ζ(2), ζ(3), ζ(4), ζ(5)的贡献
        - 这是目前已知的最高阶反常维度矩阵
        - 表达式非常复杂，包含多个色因子的组合
        """
        CF, CA = AlphaSRunner.CF, AlphaSRunner.CA
        Zeta2, Zeta3, Zeta4, Zeta5 = (
            AlphaSRunner.ZETA2,
            AlphaSRunner.ZETA3,
            AlphaSRunner.ZETA4,
            AlphaSRunner.ZETA5,
        )

        # ========== 夸克→夸克分量 (qq) ==========
        qq = (
            (-19521281 / 13891500) * CF * nf**2
            + (
                -3829448611 / 33075000
                - (5647384 * Zeta2) / 165375
                + (337208 * Zeta3) / 1575
                - (136 * Zeta4) / 3
            )
            * CF**2
            * nf
            + (
                -59504161907 / 4167450000
                + (2198426 * Zeta2) / 33075
                - (257336 * Zeta3) / 1575
                + (68 * Zeta4) / 3
            )
            * CA
            * CF
            * nf
            + (
                3623379121 / 1518750
                - (1101667 * Zeta2) / 1125
                - (235402 * Zeta3) / 75
                - 208 * Zeta2 * Zeta3
                + (51064 * Zeta4) / 15
                - 432 * Zeta5
            )
            * CA
            * CF**2
            + (
                -4180165661 / 16200000
                - (730997 * Zeta2) / 3375
                + (281558 * Zeta3) / 225
                + 48 * Zeta2 * Zeta3
                - (12752 * Zeta4) / 15
                + 112 * Zeta5
            )
            * CA**2
            * CF
            + (
                -161877491 / 81000
                + (973994 * Zeta2) / 1125
                + (29756 * Zeta3) / 15
                + 224 * Zeta2 * Zeta3
                - (7312 * Zeta4) / 3
                + 416 * Zeta5
            )
            * CF**3
        )

        # ========== 胶子→夸克分量 (qg) ==========
        qg = (
            (-34936 / 128625) * nf**3
            + (-304184494 / 86821875 + (163456 * Zeta2) / 33075) * CF * nf**2
            + (32725682 / 5788125 - (7304 * Zeta2) / 2205 - (128 * Zeta3) / 63)
            * CA
            * nf**2
            + (
                -89580154991 / 1041862500
                + (1961024 * Zeta2) / 165375
                + (104064 * Zeta3) / 1225
                - (192 * Zeta4) / 35
            )
            * CA
            * CF
            * nf
            + (
                1901298533 / 119070000
                + (18545284 * Zeta2) / 1157625
                - (7552 * Zeta3) / 735
                - (1152 * Zeta4) / 35
            )
            * CA**2
            * nf
            + (
                2857982668 / 37209375
                - (154544 * Zeta2) / 33075
                - (1073536 * Zeta3) / 11025
                + (2048 * Zeta4) / 105
            )
            * CF**2
            * nf
        )

        # ========== 夸克→胶子分量 (gq) ==========
        gq = (
            (-79552063 / 25725000 + (196 * Zeta2) / 225 - (64 * Zeta3) / 45)
            * CF**2
            * nf
            + (
                -1414558213 / 12150000
                + (91562 * Zeta2) / 3375
                + (3744 * Zeta3) / 25
                - (704 * Zeta4) / 5
            )
            * CF**3
            + (
                -778603499 / 22680000
                - (8690054 * Zeta2) / 165375
                - (3632 * Zeta3) / 1575
                + (448 * Zeta4) / 15
            )
            * CA**2
            * CF
            + (359997809 / 92610000 - (12 * Zeta2) / 5 + (416 * Zeta3) / 45)
            * CA
            * CF
            * nf
            + (
                4485362407 / 34020000
                + (13794439 * Zeta2) / 165375
                - (4160 * Zeta3) / 21
                + (1168 * Zeta4) / 15
            )
            * CA
            * CF**2
        )

        # ========== 胶子→胶子分量 (gg) ==========
        gg = (
            (-(43545391 / 52093125) - (2048 * Zeta2) / 4725) * CF * nf**2
            + (-(110295583 / 74418750) + (112936 * Zeta2) / 55125 - (256 * Zeta3) / 225)
            * CF**2
            * nf
            + (-(128595883 / 41674500) + (160 * Zeta2) / 27 - (64 * Zeta3) / 9)
            * CA
            * nf**2
            + (
                -(13864219709 / 347287500)
                - (3258284 * Zeta2) / 165375
                + (12672 * Zeta3) / 175
            )
            * CA
            * CF
            * nf
            + (
                -(88337469301 / 4167450000)
                - (4654366 * Zeta2) / 55125
                + (2336 * Zeta3) / 1575
                + 104 * Zeta4
            )
            * CA**2
            * nf
            + (
                11996763263 / 463050000
                - (185369599 * Zeta2) / 1157625
                + (698828 * Zeta3) / 11025
                + 64 * Zeta2 * Zeta3
                - (2524 * Zeta4) / 105
                + 96 * Zeta5
            )
            * CA**3
        )

        return np.array([[qq, qg], [gq, gg]])


# ========== 使用示例和测试 ==========
def run_demonstration() -> None:
    """
    运行EvolutionMatrix类的演示示例。

    展示如何使用EvolutionMatrix类进行QCD演化计算，包括：
    1. 创建演化器实例
    2. 计算演化算符
    3. 演化PDF向量
    4. 使用简化拟合算符
    """
    print("\n" + "=" * 60)
    print("EvolutionMatrix 类使用示例")
    print("=" * 60)

    # 设置演化参数
    Q0, Q_final, R = 2000.0, 4000.0, 0.4
    print(f"\n演化参数:")
    print(f"  初始能标 Q0 = {Q0:.1f} GeV")
    print(f"  目标能标 Q  = {Q_final:.1f} GeV")
    print(f"  角度参数 R  = {R:.1f}")

    # 示例1: 创建NNLO演化器
    print("\n" + "-" * 60)
    print("[示例1: NNLO演化算符计算]")
    evolver_nnlo = EvolutionMatrix(order=2)
    evolution_operator = evolver_nnlo.get_evolution_operator(Q0, Q_final, R, m=4)
    print(f"  演化算符矩阵 (4阶截断):")
    print(f"    [[{evolution_operator[0, 0]:.6f}, {evolution_operator[0, 1]:.6f}],")
    print(f"     [{evolution_operator[1, 0]:.6f}, {evolution_operator[1, 1]:.6f}]]")

    # 示例2: PDF演化
    print("\n" + "-" * 60)
    print("[示例2: PDF向量演化]")
    pdf0 = np.array([1.0, 0.5])  # 初始PDF: [夸克分量, 胶子分量]
    pdf_evolved = evolver_nnlo.evolve(pdf0, Q0, Q_final, R, m=4)
    print(f"  初始PDF (Q0={Q0:.1f} GeV): {pdf0}")
    print(f"  演化后PDF (Q={Q_final:.1f} GeV): {pdf_evolved}")
    print(
        f"  变化: Δ夸克 = {pdf_evolved[0] - pdf0[0]:+.6f}, "
        f"Δ胶子 = {pdf_evolved[1] - pdf0[1]:+.6f}"
    )

    # 示例3: 简化拟合算符
    print("\n" + "-" * 60)
    print("[示例3: 简化拟合算符 (仅LL)]")
    Q_fit, R_fit = 20.0, 0.6
    fit_operator = evolver_nnlo.get_operator_fit(Q_fit, R_fit)
    print(f"  能标 Q = {Q_fit:.1f} GeV, R = {R_fit:.1f}")
    print(f"  拟合算符矩阵:")
    print(f"    [[{fit_operator[0, 0]:.6f}, {fit_operator[0, 1]:.6f}],")
    print(f"     [{fit_operator[1, 0]:.6f}, {fit_operator[1, 1]:.6f}]]")

    # 示例4: 不同阶数对比
    print("\n" + "-" * 60)
    print("[示例4: 不同微扰阶数对比]")
    Q_test = 100.0
    for order_test in [0, 1, 2]:
        evolver_test = EvolutionMatrix(order=order_test)
        op_test = evolver_test.get_evolution_operator(Q0, Q_test, R, m=4)
        order_name = ["LO", "NLO", "NNLO"][order_test]
        print(
            f"  {order_name:4s}: U[0,0] = {op_test[0, 0]:.6f}, "
            f"U[1,1] = {op_test[1, 1]:.6f}"
        )

    # 示例5: R矩阵和U矩阵
    print("\n" + "-" * 60)
    print("[示例5: R矩阵和U矩阵]")
    r_dict, u_dict = evolver_nnlo._compute_and_cache_matrices(nf=5, m=4)
    print("  R矩阵:")
    for n, r_matrix in r_dict.items():
        print(f"    R{n}:")
        print(r_matrix)
    print("  U矩阵:")
    for n, u_matrix in u_dict.items():
        print(f"    U{n}:")
        print(u_matrix)

    print("\n" + "=" * 60)
    print("演示完成")
    print("=" * 60 + "\n")

    # 示例6： LL
    print("\n" + "-" * 60)
    print("[示例6: LL演化算符计算]")
    evolver_ll = EvolutionMatrix(order=0)
    evolution_operator = evolver_ll.get_evolution_operator(Q0, Q_final, R, m=2)
    print(f"  演化算符矩阵 (LL):")
    print(f"    [[{evolution_operator[0, 0]:.6f}, {evolution_operator[0, 1]:.6f}],")
    print(f"     [{evolution_operator[1, 0]:.6f}, {evolution_operator[1, 1]:.6f}]]")


if __name__ == "__main__":
    run_demonstration()

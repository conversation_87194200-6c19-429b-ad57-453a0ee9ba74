[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "eec-fit"
version = "0.1.0"
description = "EEC Fit - Energy-Energy Correlation Fitting Package"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "numpy",
    "scipy",
    "vegas",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "black",
    "flake8",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"
